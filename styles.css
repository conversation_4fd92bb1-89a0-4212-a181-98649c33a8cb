* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Top Header Bar */
.top-header {
    background-color: #4c51bf;
    color: white;
    padding: 8px 0;
    font-size: 14px;
}

.top-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.social-icons {
    display: flex;
    gap: 10px;
}

.social-icons a {
    color: white;
    font-size: 16px;
    text-decoration: none;
    transition: opacity 0.3s;
}

.social-icons a:hover {
    opacity: 0.8;
}

/* Main Header */
.main-header {
    background-color: #5a67d8;
    padding: 20px 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: bold;
    letter-spacing: -1px;
}

.subscribe-btn {
    background-color: #ffd700;
    color: #333;
    border: none;
    padding: 12px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s;
}

.subscribe-btn:hover {
    background-color: #ffed4e;
}

/* Navigation */
.main-nav {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    margin: 0;
}

.nav-menu a {
    display: block;
    padding: 15px 20px;
    text-decoration: none;
    color: #666;
    font-weight: 500;
    transition: color 0.3s, background-color 0.3s;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #5a67d8;
    background-color: #e2e8f0;
}

.nav-icons {
    display: flex;
    gap: 10px;
}

.search-btn,
.menu-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 18px;
    padding: 10px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.search-btn:hover,
.menu-btn:hover {
    background-color: #e2e8f0;
}

/* Banner Section */
.banner-section {
    padding: 20px 0;
    background-color: #f8f9fa;
}

.banner-ad {
    background: linear-gradient(135deg, #e53e3e 0%, #fc8181 100%);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.ad-content {
    display: flex;
    align-items: center;
    padding: 20px;
    color: white;
    position: relative;
}

.ad-logo {
    margin-right: 20px;
}

.ad-logo img {
    height: 30px;
    width: auto;
}

.ad-text {
    flex: 1;
}

.ad-text h3 {
    font-size: 1.5rem;
    margin-bottom: 5px;
    font-weight: bold;
}

.ad-text p {
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.ad-text small {
    font-size: 0.9rem;
    opacity: 0.9;
}

.ad-cta {
    margin-left: 20px;
}

.get-report-btn {
    background-color: rgba(0, 0, 0, 0.3);
    color: white;
    border: 2px solid white;
    padding: 12px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
}

.get-report-btn:hover {
    background-color: white;
    color: #e53e3e;
}

/* Main Content Section */
.main-content-section {
    padding: 40px 0;
    background-color: #ffffff;
}

.content-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 40px;
    margin-top: 20px;
}

.main-articles {
    min-width: 0;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.article-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid #e9ecef;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.article-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.article-card:hover .article-image img {
    transform: scale(1.05);
}

.article-content {
    padding: 20px;
}

.article-title {
    margin-bottom: 15px;
}

.article-title a {
    color: #2d3748;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 600;
    line-height: 1.4;
    display: block;
    transition: color 0.3s;
}

.article-title a:hover {
    color: #5a67d8;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.85rem;
    color: #666;
}

.article-meta .date,
.article-meta .sponsor {
    display: flex;
    align-items: center;
    gap: 5px;
}

.article-meta .category {
    color: #5a67d8;
    font-weight: 500;
}

.article-excerpt {
    color: #4a5568;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Articles List - Horizontal Layout */
.articles-list {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #e9ecef;
}

.article-item {
    display: flex;
    gap: 15px;
    padding: 20px 0;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.3s;
}

.article-item:hover {
    background-color: #f8f9fa;
}

.article-item:last-child {
    border-bottom: none;
}

.article-image-small {
    width: 120px;
    height: 80px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;
}

.article-image-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.article-item:hover .article-image-small img {
    transform: scale(1.05);
}

.article-content-small {
    flex: 1;
    min-width: 0;
}

.article-title-small {
    margin-bottom: 8px;
}

.article-title-small a {
    color: #2d3748;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
    display: block;
    transition: color 0.3s;
}

.article-title-small a:hover {
    color: #5a67d8;
}

.article-meta-small {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 8px;
    font-size: 0.8rem;
    color: #666;
}

.article-meta-small .date,
.article-meta-small .sponsor {
    display: flex;
    align-items: center;
    gap: 4px;
}

.article-meta-small .category {
    color: #5a67d8;
    font-weight: 500;
}

.article-excerpt-small {
    color: #4a5568;
    line-height: 1.5;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* CISO Board Report Ad */
.sidebar-ad {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.ciso-ad {
    background-color: #1e40af;
    color: white;
    position: relative;
}

.ad-header {
    padding: 8px 15px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.ad-content-ciso {
    padding: 15px;
    position: relative;
}

.ad-content-ciso h3 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.ad-content-ciso p {
    font-size: 0.9rem;
    margin-bottom: 15px;
    opacity: 0.9;
}

.ad-visual {
    height: 120px;
    position: relative;
    margin-bottom: 15px;
}

.dashboard-mockup {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    width: 70%;
    height: 80%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    overflow: hidden;
}

.dashboard-header {
    height: 20%;
    background-color: rgba(255, 255, 255, 0.3);
}

.dashboard-chart {
    position: relative;
    height: 80%;
}

.chart-arc {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 10px solid #ff6b6b;
    border-radius: 50%;
    border-bottom-color: transparent;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
}

.ad-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wiz-logo {
    font-weight: bold;
    font-size: 1.2rem;
}

.download-btn {
    background-color: white;
    color: #1e40af;
    border: none;
    border-radius: 15px;
    padding: 5px 12px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
}

/* Zscaler Ad */
.zscaler-ad {
    background: linear-gradient(135deg, #4a00e0 0%, #8e2de2 100%);
    color: white;
}

.zscaler-header {
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.2);
}

.zscaler-content {
    padding: 20px;
}

.zscaler-content h3 {
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1.3;
    margin-bottom: 20px;
}

.replace-tag {
    background-color: #ff00cc;
    color: white;
    display: inline-block;
    padding: 8px 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    border-radius: 3px;
}

/* Trending News Section */
.trending-section {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.trending-title {
    padding: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;
}

.trending-item {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
}

.trending-item:last-child {
    border-bottom: none;
}

.trending-image {
    width: 60px;
    height: 60px;
    border-radius: 5px;
    overflow: hidden;
    margin-right: 15px;
    flex-shrink: 0;
}

.trending-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.trending-content h4 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
    line-height: 1.4;
}

.trending-content h4 a {
    color: #2d3748;
    text-decoration: none;
    transition: color 0.3s;
}

.trending-content h4 a:hover {
    color: #5a67d8;
}

.trending-meta {
    font-size: 0.8rem;
    color: #a0aec0;
}

/* Popular Resources Section */
.popular-resources-section {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #e9ecef;
    margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .content-layout {
        grid-template-columns: 1fr;
    }

    .sidebar {
        order: -1;
    }

    .article-item {
        flex-direction: column;
        gap: 12px;
        padding: 15px 0;
    }

    .article-image-small {
        width: 100%;
        height: 150px;
    }

    .article-title-small a {
        font-size: 1.1rem;
    }

    .article-meta-small {
        font-size: 0.85rem;
    }

    .article-excerpt-small {
        font-size: 0.95rem;
        -webkit-line-clamp: 3;
    }
}

@media (max-width: 768px) {
    .top-header .container {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .logo h1 {
        font-size: 2rem;
    }

    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-menu a {
        padding: 10px 15px;
        font-size: 14px;
    }

    .ad-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .ad-logo,
    .ad-cta {
        margin: 0;
    }

    .articles-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .article-content {
        padding: 15px;
    }

    .article-meta {
        flex-direction: column;
        gap: 8px;
    }
}

/* Popular Resources Widget */
.popular-resources-widget {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #e9ecef;
    margin-top: 20px;
}

.widget-title {
    padding: 15px 20px;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #333;
    margin: 0;
    background-color: #f8f9fa;
}

.widget-title i {
    font-size: 0.9rem;
    color: #666;
}

.popular-resource-item {
    display: flex;
    padding: 18px 20px;
    border-bottom: 1px solid #f0f0f0;
    align-items: flex-start;
    transition: background-color 0.2s ease;
}

.popular-resource-item:hover {
    background-color: #f8f9fa;
}

.popular-resource-item:last-child {
    border-bottom: none;
}

.popular-resource-image {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    overflow: hidden;
    margin-right: 18px;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.popular-resource-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.popular-resource-content {
    flex: 1;
}

.popular-resource-content h4 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0;
    line-height: 1.4;
}

.popular-resource-content h4 a {
    color: #4a5568;
    text-decoration: none;
    transition: color 0.3s ease;
    display: block;
}

.popular-resource-content h4 a:hover {
    color: #007bff;
}

/* Article Page Styles */
.article-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    margin-bottom: 40px;
}

.article-header {
    margin-bottom: 30px;
}

.breadcrumb {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 15px;
}

.breadcrumb a {
    color: #5a67d8;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.article-page h1 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1.3;
    margin-bottom: 20px;
}

.article-meta-full {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    font-size: 0.9rem;
    color: #666;
    padding: 15px 0;
    border-top: 1px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
}

.article-meta-full span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.article-image-full {
    margin: 30px 0;
    border-radius: 10px;
    overflow: hidden;
}

.article-image-full img {
    width: 100%;
    height: auto;
    display: block;
}

.image-caption {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.article-image-inline {
    margin: 30px 0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.article-image-inline img {
    width: 100%;
    height: auto;
    display: block;
}

.article-content-full {
    line-height: 1.7;
    font-size: 1.1rem;
    color: #2d3748;
}

.article-content-full .lead {
    font-size: 1.3rem;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f7fafc;
    border-left: 4px solid #5a67d8;
    border-radius: 5px;
}

.article-content-full h2 {
    font-size: 1.6rem;
    font-weight: 600;
    color: #2d3748;
    margin: 40px 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.article-content-full h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
    margin: 30px 0 15px 0;
}

.article-content-full h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #4a5568;
    margin: 25px 0 10px 0;
}

.article-content-full p {
    margin-bottom: 20px;
}

.article-content-full ul, .article-content-full ol {
    margin: 20px 0;
    padding-left: 30px;
}

.article-content-full li {
    margin-bottom: 8px;
}

.article-footer {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tag {
    background-color: #5a67d8;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.share-buttons {
    display: flex;
    gap: 10px;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 15px;
    background-color: #f7fafc;
    color: #4a5568;
    text-decoration: none;
    border-radius: 5px;
    font-size: 0.9rem;
    transition: all 0.3s;
}

.share-btn:hover {
    background-color: #5a67d8;
    color: white;
}

/* Related Articles Section */
.related-articles {
    background-color: #f7fafc;
    padding: 40px 0;
}

.related-articles h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 30px;
    text-align: center;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.related-item {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}

.related-item:hover {
    transform: translateY(-5px);
}

.related-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.related-item h4 {
    padding: 15px;
    margin: 0;
    font-size: 1rem;
    line-height: 1.4;
}

.related-item h4 a {
    color: #2d3748;
    text-decoration: none;
}

.related-item h4 a:hover {
    color: #5a67d8;
}

/* Responsive Image Styles */
@media (max-width: 768px) {
    .article-image-full {
        margin: 20px -20px;
        border-radius: 0;
    }

    .article-image-inline {
        margin: 20px -20px;
        border-radius: 0;
    }

    .image-caption {
        padding: 10px 20px;
    }

    .related-grid {
        grid-template-columns: 1fr;
    }

    .article-page {
        margin: 10px;
        padding: 15px;
    }

    .article-page h1 {
        font-size: 1.8rem;
    }

    .article-meta-full {
        flex-direction: column;
        gap: 10px;
    }
}

/* Image hover effects */
.article-image-inline img:hover,
.article-image-full img:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

.related-item img:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

/* Wiz CISO Banner */
.wiz-ciso-banner {
    display: flex;
    justify-content: center;
    margin: 20px 0;
    padding: 20px;
}

.wiz-banner-link {
    display: block;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    max-width: 300px;
}

.wiz-banner-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.wiz-banner-image {
    width: 100%;
    height: auto;
    display: block;
}

/* Article with Sidebar Ad Layout */
.article-with-sidebar {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.article-main-content {
    flex: 1;
    display: flex;
    gap: 15px;
}

.article-sidebar-ad {
    flex-shrink: 0;
    width: 300px;
}

.wiz-ciso-ad {
    display: block;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.wiz-ciso-ad:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.wiz-ad-image {
    width: 100%;
    height: auto;
    display: block;
}

/* Responsive adjustments for article with sidebar */
@media (max-width: 768px) {
    .article-with-sidebar {
        flex-direction: column;
        gap: 15px;
    }

    .article-sidebar-ad {
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    .article-main-content {
        width: 100%;
    }
}

/* Wiz CISO Report Sidebar Ad */
.wiz-ciso-report-ad {
    margin-bottom: 30px;
}

.wiz-ciso-link {
    display: block;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.wiz-ciso-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.wiz-ciso-image {
    width: 100%;
    height: auto;
    display: block;
}

/* Article with Right Ad Layout */
.article-with-right-ad {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.article-with-right-ad .article-main-content {
    flex: 1;
    display: flex;
    gap: 15px;
}

.article-right-ad {
    flex-shrink: 0;
    width: 250px;
}

.wiz-article-ad {
    display: block;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.wiz-article-ad:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.wiz-article-image {
    width: 100%;
    height: auto;
    display: block;
}

/* Responsive adjustments for article with right ad */
@media (max-width: 768px) {
    .article-with-right-ad {
        flex-direction: column;
        gap: 15px;
    }

    .article-right-ad {
        width: 100%;
        max-width: 250px;
        margin: 0 auto;
    }
}

/* Zscaler ThreatLabz Sidebar Ad */
.zscaler-threatlabz-ad {
    margin-bottom: 30px;
}

.zscaler-threatlabz-link {
    display: block;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.zscaler-threatlabz-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.zscaler-threatlabz-image {
    width: 100%;
    height: auto;
    display: block;
}

/* Zscaler ThreatLabz Banner */
.zscaler-threatlabz-banner {
    display: flex;
    justify-content: center;
    margin: 0;
    padding: 0 20px;
    background: transparent;
    border: none;
}

.zscaler-banner-link {
    display: block;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
    transition: transform 0.3s ease, opacity 0.3s ease;
    max-width: 90%;
    width: 90%;
}

.zscaler-banner-link:hover {
    transform: scale(1.02);
    opacity: 0.95;
}

.zscaler-banner-image {
    width: 100%;
    height: auto;
    display: block;
    border: none;
    outline: none;
}

/* Remove any red lines or borders from banner section */
.banner-section {
    border: none !important;
    border-bottom: none !important;
    margin-bottom: 0 !important;
}

.banner-content {
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Zscaler Security Sidebar Ad */
.zscaler-security-ad {
    margin-bottom: 30px;
}

.zscaler-security-link {
    display: block;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.zscaler-security-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.zscaler-security-image {
    width: 100%;
    height: auto;
    display: block;
}

/* Newsletter Subscription Styles */
.newsletter-section {
    padding: 50px 0;
    background: #f8f9fa;
}

.newsletter-card {
    background: linear-gradient(135deg, #4c63d2 0%, #5a67d8 100%);
    border-radius: 20px;
    padding: 50px 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(76, 99, 210, 0.3);
    max-width: 800px;
    margin: 0 auto;
}

.newsletter-title {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
    line-height: 1.3;
}

.newsletter-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 35px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-form {
    max-width: 500px;
    margin: 0 auto;
}

.form-group {
    display: flex;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.email-input {
    flex: 1;
    padding: 18px 20px;
    border: none;
    font-size: 1rem;
    outline: none;
    background: transparent;
}

.email-input::placeholder {
    color: #9ca3af;
}

.subscribe-btn {
    background: #4c63d2;
    border: none;
    padding: 18px 25px;
    color: white;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.subscribe-btn:hover {
    background: #3b52cc;
}

.subscribe-btn i {
    font-size: 1.1rem;
}

/* Footer Section */
.footer-section {
    background-color: #f8f9fa;
    padding: 50px 0 30px;
    border-top: 1px solid #e9ecef;
}

/* Social Media Section */
.social-section {
    text-align: center;
    margin-bottom: 50px;
}

.social-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 30px;
}

.social-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 25px;
    max-width: 1000px;
    margin: 0 auto;
}

.social-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-item:hover {
    transform: translateY(-5px);
}

.social-item a {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: inherit;
    transition: transform 0.3s ease;
}

.social-item a:hover {
    transform: translateY(-5px);
}

.social-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.social-icon:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.social-icon i {
    font-size: 1.5rem;
    color: white;
}

.social-icon.twitter {
    background: #1da1f2;
}

.social-icon.linkedin {
    background: #0077b5;
}

.social-icon.youtube {
    background: #ff0000;
}

.social-icon.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-icon.facebook {
    background: #1877f2;
}

.social-icon.google-news {
    background: #4285f4;
}

.follower-count {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* Footer Links Section */
.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
    padding: 0 20px;
}

.footer-column h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-column ul li {
    margin-bottom: 8px;
}

.footer-column ul li a {
    color: #6c757d;
    text-decoration: none;
    font-size: 0.95rem;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-column ul li a:hover {
    color: #007bff;
}

.footer-column ul li a i {
    font-size: 0.9rem;
}

/* Footer Bottom Section */
.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid #e9ecef;
    position: relative;
}

.footer-bottom p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.footer-extra {
    position: absolute;
    bottom: 10px;
    right: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 0.8rem;
    color: #999;
}

.activate-windows {
    font-weight: 500;
    margin-bottom: 2px;
}

.settings-text {
    font-size: 0.75rem;
}

/* Pagination Section */
.pagination-section {
    background-color: #ffffff;
    padding: 30px 0;
    border-top: 1px solid #e9ecef;
}

.pagination-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.prev-page-btn,
.next-page-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    text-decoration: none;
    font-weight: 500;
    padding: 12px 24px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.prev-page-btn:hover,
.next-page-btn:hover {
    color: #495057;
    background-color: #e9ecef;
    border-color: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Responsive pagination */
@media (max-width: 768px) {
    .pagination-nav {
        flex-direction: column;
        gap: 15px;
    }

    .prev-page-btn,
    .next-page-btn {
        width: 100%;
        justify-content: center;
    }

    .resources-grid {
        grid-template-columns: 1fr;
    }

    .resource-card {
        margin-bottom: 20px;
    }
}

/* Expert Insights Section */
.expert-insights-section {
    padding: 40px 0;
    background-color: #ffffff;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-title i {
    color: #007bff;
}

.section-tabs {
    display: flex;
    gap: 20px;
}

.tab-link {
    color: #6c757d;
    text-decoration: none;
    font-weight: 500;
    padding: 5px 0;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-link.active,
.tab-link:hover {
    color: #007bff;
    border-bottom-color: #007bff;
}

.expert-insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 10px;
}

.expert-insight-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    max-width: 200px;
}

.expert-insight-item:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.expert-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 6px;
}

.expert-info {
    display: flex;
    gap: 6px;
    align-items: flex-start;
}

.expert-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
}

.expert-avatar-large {
    width: 40px;
    height: 40px;
}

.expert-details h4 {
    font-size: 0.6rem;
    font-weight: 600;
    color: #333;
    margin: 0;
    line-height: 1.2;
}

.expert-details p {
    font-size: 0.5rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.1;
}

.company-logo img {
    height: 12px;
    width: auto;
}

.insight-accent {
    width: 2px;
    height: 15px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border-radius: 1px;
}

.insight-content h3 {
    font-size: 0.65rem;
    line-height: 1.2;
    margin-bottom: 4px;
}

.insight-content h3 a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.insight-content h3 a:hover {
    color: #007bff;
}

.insight-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.insight-date {
    font-size: 0.55rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 2px;
}

.read-more {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.6rem;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: #0056b3;
}

/* Cybersecurity Resources Section */
.cybersecurity-resources-section {
    padding: 50px 0;
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
}

.cybersecurity-resources-section .section-header {
    margin-bottom: 40px;
    border-bottom: none;
    padding-bottom: 0;
}

.cybersecurity-resources-section .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.cybersecurity-resources-section .section-title i {
    color: #333;
    font-size: 1rem;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.resource-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.resource-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-3px);
}

.resource-image {
    width: 100%;
    height: 180px;
    overflow: hidden;
}

.resource-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.resource-card:hover .resource-image img {
    transform: scale(1.05);
}

.resource-content {
    padding: 20px;
}

.resource-content h3 {
    font-size: 1rem;
    line-height: 1.4;
    margin-bottom: 12px;
}

.resource-content h3 a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.resource-content h3 a:hover {
    color: #007bff;
}

.resource-description {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.5;
    margin: 0;
}

/* Newsletter Mobile Responsive */
@media (max-width: 768px) {
    .newsletter-card {
        padding: 35px 25px;
        margin: 0 20px;
    }

    .newsletter-title {
        font-size: 1.6rem;
    }

    .newsletter-description {
        font-size: 1rem;
    }

    .form-group {
        flex-direction: column;
    }

    .subscribe-btn {
        border-radius: 0 0 12px 12px;
    }

    /* Footer Mobile Responsive */
    .social-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .social-title {
        font-size: 1.5rem;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .footer-extra {
        position: static;
        margin-top: 20px;
        align-items: center;
    }

    /* Popular Resources Mobile */
    .popular-resource-item {
        padding: 15px;
    }

    .popular-resource-image {
        width: 70px;
        height: 70px;
        margin-right: 15px;
    }

    .popular-resource-content h4 {
        font-size: 0.95rem;
    }

    .widget-title {
        padding: 12px 15px;
        font-size: 1rem;
    }
}

/* Page Header Styles */
.page-header {
    background-color: #f8f9fa;
    padding: 40px 0;
    border-bottom: 1px solid #e9ecef;
}

.page-header-content {
    text-align: center;
}

.category-header {
    margin-bottom: 20px;
}

.category-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #5a67d8;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 15px 0;
}

.page-header p {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Main Content for Category Pages */
.main-content {
    padding: 40px 0;
    background-color: #ffffff;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;
}

.featured-article {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

.featured-article .article-image {
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.featured-article .article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-article:hover .article-image img {
    transform: scale(1.05);
}

.featured-article .article-content {
    padding: 30px;
}

.featured-article h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1.3;
    margin-bottom: 15px;
}

.featured-article p {
    font-size: 1.1rem;
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 20px;
}

.featured-article .read-more {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #5a67d8;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.featured-article .read-more:hover {
    color: #4c51bf;
}

/* Articles Grid for Category Pages */
.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

/* Responsive Design for Category Pages */
@media (max-width: 768px) {
    .page-header {
        padding: 30px 0;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .category-title {
        font-size: 1rem;
    }

    .featured-article .article-image {
        height: 200px;
    }

    .featured-article .article-content {
        padding: 20px;
    }

    .featured-article h2 {
        font-size: 1.5rem;
    }

    .articles-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

/* Expert Card - Exact Match Style */
.expert-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 0;
    text-decoration: none;
    color: inherit;
    display: block;
    max-width: 380px;
    margin: 0 auto;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    z-index: 1;
}

/* Debug - temporary red border to test clickability */
.expert-card:active {
    border: 2px solid red;
}

.expert-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
    transform: translateY(-2px);
}

.expert-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 20px 20px 15px;
    position: relative;
}

.expert-card-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.expert-card-info {
    flex: 1;
}

.expert-card-name {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.expert-card-title {
    font-size: 0.85rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.3;
}

.expert-card-accent {
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: #ffd700;
}

.expert-card-content {
    padding: 0 20px 20px;
}

.expert-card-article-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    line-height: 1.4;
    margin: 0 0 20px 0;
}

.expert-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.expert-card-date {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.85rem;
    color: #6c757d;
}

.expert-card-read {
    color: #007bff;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
}

/* Minimal Video Card Styles */
.video-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    text-decoration: none;
    color: inherit;
    display: block;
    max-width: 320px;
    margin: 0 auto;
    transition: border-color 0.2s ease;
}

.video-card:hover {
    border-color: #007bff;
    text-decoration: none;
    color: inherit;
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 140px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.video-duration {
    position: absolute;
    bottom: 6px;
    right: 6px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
}

.video-card .card-content {
    padding: 12px;
}

/* Minimal Grid Layout */
.simple-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin: 24px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .simple-grid {
        grid-template-columns: 1fr;
        gap: 12px;
        margin: 16px 0;
    }

    .simple-card,
    .video-card {
        max-width: 100%;
    }
}

/* Webinars Page Styles */
.webinars-hero {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    padding: 80px 0 60px;
    position: relative;
    overflow: hidden;
}

.webinars-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    color: #ffffff;
    font-size: 48px;
    font-weight: 700;
    margin: 0 0 20px;
    line-height: 1.2;
}

.hero-underline {
    width: 80px;
    height: 4px;
    background: #ff6b35;
    margin: 0 auto 30px;
    border-radius: 2px;
}

.hero-description {
    color: #cccccc;
    font-size: 18px;
    line-height: 1.6;
    margin: 0 0 20px;
}

.hero-cta {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

/* Webinars Sections */
.upcoming-webinars-section {
    padding: 60px 0;
    background: #f8f9fa;
}

.upcoming-webinars-section .section-header {
    text-align: center;
    margin-bottom: 50px;
}

.upcoming-webinars-section .section-title {
    color: #1a1a1a;
    font-size: 36px;
    font-weight: 700;
    margin: 0;
    position: relative;
    display: inline-block;
}

.upcoming-webinars-section .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: #ff6b35;
    border-radius: 2px;
}

/* Webinars Grid */
.webinars-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

/* Bottom webinars container - 2 webinars side by side */
.bottom-webinars-container {
    grid-column: 1 / 4;
    display: flex;
    gap: 30px;
    justify-content: center;
    margin-top: 30px;
}

.bottom-webinars-container .webinar-card {
    max-width: 280px;
    transform: scale(0.9);
}

/* Recent Webinars Section - New Design */
.recent-webinars-section {
    padding: 80px 0;
    background: #f8fafc;
}

.recent-webinars-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 40px;
}

/* Recent Webinar Cards - New Style */
.recent-webinar-card {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.recent-webinar-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Recent Banner Styling - New Design */
.recent-banner {
    position: relative;
    padding: 25px;
    color: white;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Banner Color Variants */
.recent-banner.banner-orange {
    background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
}

.recent-banner.banner-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
}

.recent-banner.banner-purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.recent-banner.banner-green {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.recent-banner.banner-teal {
    background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
}

.recent-banner.banner-indigo {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
}

.recent-banner.banner-pink {
    background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
}

.recent-banner.banner-yellow {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.recent-banner-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.recent-brand-text {
    font-weight: 600;
    font-size: 16px;
    color: white;
}

.recent-live-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
    color: white;
}

.recent-live-badge::before {
    content: "●";
    color: #fff;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.recent-webinar-title {
    font-size: 20px;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 20px;
    color: white;
}

.recent-speakers {
    display: flex;
    gap: 15px;
    align-items: center;
}

.recent-speaker {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.recent-speaker-photo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    margin-bottom: 5px;
    object-fit: cover;
}

.recent-speaker-name {
    font-size: 12px;
    font-weight: 600;
    opacity: 0.9;
    color: white;
}

.recent-speaker-title {
    font-size: 10px;
    opacity: 0.7;
    color: white;
}

/* Webinar Card Content - New Style */
.recent-webinar-content {
    padding: 25px;
    background: #f8fafc;
}

.recent-webinar-description {
    color: #64748b;
    font-size: 14px;
    line-height: 1.6;
    font-weight: 500;
    margin: 0;
}

/* Brand-specific banner colors */
.banner-okta {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
}

.banner-doppel {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.banner-sentra {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.banner-crowdstrike {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.banner-astrix {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
}

.banner-checkmarx {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.banner-paloalto {
    background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
}

.banner-auth0 {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .recent-webinars-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 25px;
    }
}

@media (max-width: 900px) {
    .recent-webinars-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .recent-banner {
        height: 150px;
    }

    .recent-webinar-title {
        font-size: 0.85rem;
        -webkit-line-clamp: 2;
    }

    .recent-webinar-content h3 {
        font-size: 0.9rem;
    }

    .recent-webinar-description {
        font-size: 0.8rem;
        -webkit-line-clamp: 2;
    }
}

@media (max-width: 600px) {
    .recent-webinars-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .recent-webinars-section {
        padding: 60px 0;
    }

    .recent-banner {
        height: 140px;
    }

    .recent-webinar-title {
        font-size: 0.8rem;
        -webkit-line-clamp: 2;
    }

    .recent-webinar-content h3 {
        font-size: 0.9rem;
        -webkit-line-clamp: 2;
    }

    .recent-webinar-description {
        font-size: 0.8rem;
        -webkit-line-clamp: 2;
        margin-bottom: 12px;
    }

    .recent-speaker-info span {
        font-size: 0.75rem;
    }

    .recent-speaker-avatar {
        width: 24px;
        height: 24px;
    }

    .recent-company-logo {
        height: 16px;
    }
}

.recent-webinars-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.recent-webinars-grid::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.recent-webinars-grid::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

@media (max-width: 768px) {
    .recent-webinars-grid {
        gap: 15px;
        padding: 15px 0;
    }

    .webinar-card.recent {
        min-width: 250px;
    }
}

@media (max-width: 480px) {
    .recent-webinars-grid {
        gap: 12px;
        padding: 12px 0;
    }

    .webinar-card.recent {
        min-width: 220px;
    }
}

/* Professional Webinars Grid */
.professional-webinars-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

@media (max-width: 768px) {
    .professional-webinars-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .professional-webinars-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

/* Professional Webinar Cards */
.professional-webinar-card {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    border: 1px solid #e0e0e0;
}

.professional-webinar-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.professional-webinar-image {
    position: relative;
    height: 200px;
    overflow: hidden;
    background: #f8f9fa;
}

.professional-webinar-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
    transition: transform 0.3s ease;
}

.professional-webinar-card:hover .professional-webinar-image img {
    transform: scale(1.05);
}

.professional-webinar-content {
    padding: 20px;
}

.professional-webinar-content h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 12px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.professional-webinar-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Webinar Cards */
.webinar-card {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.webinar-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.webinar-card.upcoming {
    border-top: none;
}

.webinar-card.recent {
    border-top: none;
}

.webinar-card.recent {
    border-top: 4px solid #6c757d;
    min-width: 280px;
    flex-shrink: 0;
}

.webinar-banner {
    position: relative;
    height: 220px;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
    background: #f8f9fa;
}

.webinar-logo {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 3;
    display: flex;
    align-items: center;
    gap: 10px;
}

.webinar-logo img {
    width: 80px;
    height: auto;
}

.live-webinar,
.on-demand {
    background: #ff6b35;
    color: #ffffff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.on-demand {
    background: #6c757d;
}

.webinar-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.webinar-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
    border-radius: 8px 8px 0 0;
    transition: transform 0.3s ease;
}

.webinar-card:hover .webinar-image img {
    transform: scale(1.05);
}

/* Ensure proper banner settling */
.webinar-card.recent {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.webinar-card.recent:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}



.speaker-info {
    position: absolute;
    bottom: 15px;
    left: 15px;
    z-index: 3;
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 8px 12px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.speaker-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #ffffff;
}

.speaker-details {
    display: flex;
    flex-direction: column;
}

.speaker-name {
    color: #ffffff;
    font-size: 12px;
    font-weight: 600;
    margin: 0;
}

.speaker-title {
    color: #cccccc;
    font-size: 10px;
    margin: 0;
}

.partner-logo {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 3;
    background: rgba(255, 255, 255, 0.9);
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    color: #1a1a1a;
}

.webinar-content {
    padding: 20px;
}

.webinar-content h3 {
    color: #1a1a1a;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px;
    line-height: 1.4;
}

.webinar-meta {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.webinar-meta span {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-size: 14px;
}

.webinar-meta i {
    color: #ff6b35;
    width: 16px;
}

.webinar-date {
    font-weight: 600;
}

.webinar-duration,
.webinar-views {
    color: #999999;
}

/* Responsive Webinars */
@media (max-width: 768px) {
    .hero-title {
        font-size: 36px;
    }

    .hero-description {
        font-size: 16px;
    }

    .upcoming-webinars-section .section-title {
        font-size: 28px;
    }

    .webinars-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .bottom-webinars-container {
        flex-direction: column;
        align-items: center;
    }

    .bottom-webinars-container .webinar-card {
        max-width: 100%;
    }

    .webinar-banner {
        height: 200px;
    }

    .webinar-content {
        padding: 15px;
    }

    .webinar-content h3 {
        font-size: 16px;
    }

    .upcoming-webinars-section {
        padding: 40px 0;
    }
}

/* Webinars Footer Styles */
.webinars-footer {
    background-color: #f8f9fa;
    padding: 50px 0 30px;
    border-top: 1px solid #e9ecef;
}

.connect-section {
    text-align: center;
    margin-bottom: 40px;
}

.connect-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 30px;
}

.social-icons-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.social-icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
}

.social-icon-item a {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: inherit;
    transition: transform 0.3s ease;
}

.social-icon-item a:hover {
    transform: translateY(-3px);
}

.social-icon-box {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.social-icon-box:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.social-icon-box i {
    font-size: 1.2rem;
    color: white;
}

.social-icon-box.twitter {
    background: #1da1f2;
}

.social-icon-box.linkedin {
    background: #0077b5;
}

.social-icon-box.youtube {
    background: #ff0000;
}

.social-icon-box.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-icon-box.facebook {
    background: #1877f2;
}

.social-icon-box.google-news {
    background: #4285f4;
}

.follower-text {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
    text-align: center;
}

.footer-links-simple {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-bottom: 30px;
    padding: 0 20px;
}

.footer-column-simple h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.footer-column-simple ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-column-simple ul li {
    margin-bottom: 8px;
}

.footer-column-simple ul li a {
    color: #6c757d;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-column-simple ul li a:hover {
    color: #007bff;
}

.footer-column-simple ul li a i {
    font-size: 0.8rem;
}

.copyright-section {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    position: relative;
}

.copyright-section p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.activate-windows-section {
    position: absolute;
    bottom: 10px;
    right: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 0.8rem;
    color: #999;
}

.activate-windows {
    font-weight: 500;
    margin-bottom: 2px;
}

.settings-text {
    font-size: 0.75rem;
}

/* Responsive Footer */
@media (max-width: 768px) {
    .social-icons-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }

    .footer-links-simple {
        grid-template-columns: 1fr;
        gap: 25px;
        text-align: center;
    }

    .activate-windows-section {
        position: static;
        margin-top: 15px;
        align-items: center;
    }

    .connect-title {
        font-size: 1.3rem;
    }
}

/* Professional Webinars Section - Banner Style */
.upcoming-webinars {
    background: #f8f9fa;
    padding: 80px 0;
}

.upcoming-webinars .section-header h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 50px;
    text-align: center;
    font-weight: 700;
}

.accent-text {
    color: #3498db;
    font-weight: 400;
}

.professional-webinars-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.professional-webinar-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #e9ecef;
}

.professional-webinar-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

/* Professional Banner */
.professional-banner {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
    padding: 20px;
    color: white;
    position: relative;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.professional-banner.banner-blue {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
}

.professional-banner.banner-teal {
    background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
}

.professional-banner.banner-orange {
    background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
}

.professional-banner.banner-astrix {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
}

/* Banner with Background Image */
.professional-banner.banner-with-image {
    background: none;
    position: relative;
    overflow: hidden;
}

.banner-background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.banner-bg-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.banner-overlay {
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg, rgba(44, 90, 160, 0.9) 0%, rgba(30, 58, 138, 0.9) 100%);
    width: 100%;
    height: 100%;
    padding: 20px;
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 180px;
}

.banner-overlay.banner-blue-overlay {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.9) 0%, rgba(30, 58, 138, 0.9) 100%);
}

.banner-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.brand-logo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.brand-text {
    font-weight: 700;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.live-badge {
    background: rgba(255,255,255,0.2);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.banner-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.webinar-title-section {
    margin-bottom: 20px;
}

.webinar-main-title {
    font-size: 1.1rem;
    font-weight: 700;
    line-height: 1.3;
    margin: 0;
    color: white;
}

.speakers-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.speaker-profile {
    display: flex;
    align-items: center;
    gap: 12px;
}

.speaker-photo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255,255,255,0.3);
}

.speaker-details {
    display: flex;
    flex-direction: column;
}

.speaker-name {
    font-weight: 600;
    font-size: 0.85rem;
    color: white;
    margin-bottom: 2px;
}

.speaker-role {
    font-size: 0.7rem;
    color: rgba(255,255,255,0.8);
}

.additional-speaker {
    margin-left: -10px;
}

.additional-speaker .speaker-photo {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255,255,255,0.3);
}

.partner-brand {
    display: flex;
    align-items: center;
}

.partner-logo {
    background: rgba(255,255,255,0.15);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    backdrop-filter: blur(10px);
}

/* Webinar Description */
.webinar-description {
    padding: 20px;
}

.webinar-description p {
    color: #5a6c7d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
    font-weight: 500;
}

/* Responsive Design for Upcoming Webinars */
@media (max-width: 768px) {
    .upcoming-webinars {
        padding: 60px 0;
    }

    .upcoming-webinars .section-header h2 {
        font-size: 2.2rem;
        margin-bottom: 40px;
    }

    .upcoming-grid {
        grid-template-columns: 1fr;
        gap: 25px;
        margin-top: 30px;
    }

    .card-content {
        padding: 25px 20px 20px;
    }

    .card-content h3 {
        font-size: 1.1rem;
        min-height: auto;
    }

    .speaker-row {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .speaker {
        justify-content: center;
    }

    .card-footer {
        padding: 0 20px 25px;
    }
}

@media (max-width: 480px) {
    .upcoming-webinars .section-header h2 {
        font-size: 1.8rem;
    }

    .card-header {
        padding: 15px 20px;
    }

    .brand {
        font-size: 0.9rem;
    }

    .live-indicator {
        font-size: 0.7rem;
        padding: 4px 8px;
    }
}
